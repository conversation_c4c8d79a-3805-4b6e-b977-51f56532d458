%% v0.7：优化指令 
0. 处理体重数据。
1. 生成周报：
	1. 日记位置："00周期笔记/Daily"
	2. 日记范围：<% tp.date.now("YYYY-MM-DD", -7) %> ~ <% tp.date.now("YYYY-MM-DD", -1) %>
	3. 根据日记位置、日记范围，找对应的日记，完成我之书汇总；
	4. 参考我之书汇总、上一周的总结，以 INTJ 的特质，完成本周总结和下周计划
		- 核心的第一行内容为体重8天（<% tp.date.now("YYYY-MM-DD", -7) %> ~ <% tp.date.now("YYYY-MM-DD", 0) %>）的变化，格式为：开始体重->结束体重，例如：72.0->65.0，体重记录就在本文件中。
%%

# 周总结与计划

## 本周总结
- **核心**：一周开始体重->结束体重。
- **身体**：
- **精神**：
- **财富**
- **社交**：

## 下周计划
- **身体**：
- **精神**：
- **财富**
- **社交**：

# 我之书汇总

## 每日记录
%% 表格：
- 第1列为日报的文件名（按照周报的文件名正序排列），表头为“日期”，, 添加[[]]，显示为' [[xx]] '内部链接格式，方便访问；
- 第2列直接从日报中把“【我之书】”那一行的内容复制过来，不需要变动 %%

## 体重记录
%% [体重记录](https://doc.weixin.qq.com/smartsheet/s3_AWQAwgbbAJkqwB3lhWaTmitrr4su5?scode=ALwA9AclADgo0IymYl&tab=t43KoF&viewId=vQQDlM)：根据这张图汇总上周日到本周日的数据：第一列是日期（正序排列），第二列是对应体重，第三列是备注，日期格式为年-月-日，删除没有体重的日期 %%

| 日期         | 体重   | 备注                            |
| ---------- | ---- | ----------------------------- |


